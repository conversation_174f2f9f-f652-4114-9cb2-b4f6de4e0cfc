# Drupal to WordPress Migration

This directory contains scripts and tools to migrate content from the Drupal 7 site to WordPress.

## Migration Overview

**Source:** Drupal 7.59 site with the following content types:
- Articles (main content)
- Pages (static content) 
- Events
- Gallery
- News (latest_news)
- Success Stories
- Slideshow
- Webform (Contact Us)

**Target:** WordPress site with equivalent post types and content structure.

## Files

- `drupal_export.php` - Exports content from Drupal database
- `wordpress_import.php` - Imports content into WordPress
- `config.php` - Configuration settings
- `content_mapping.json` - Content type and field mappings
- `media_migration.php` - Handles file and image migration
- `cleanup.php` - Post-migration cleanup tasks

## Usage

1. Configure database settings in `config.php`
2. Run `php drupal_export.php` to export Drupal content
3. Set up WordPress database connection
4. Run `php wordpress_import.php` to import content
5. Run `php media_migration.php` to migrate files
6. Run `php cleanup.php` for final cleanup

## Content Mapping

- **Drupal Articles** → WordPress Posts (category: Articles)
- **Drupal Pages** → WordPress Pages
- **Drupal Events** → WordPress Posts (category: Events)
- **Drupal News** → WordPress Posts (category: News)
- **Drupal Success Stories** → WordPress Posts (category: Success Stories)
- **Drupal Gallery** → WordPress Posts (category: Gallery)

## Requirements

- PHP 7.4+
- MySQL/MariaDB access to both Drupal and WordPress databases
- WordPress installation ready for import
