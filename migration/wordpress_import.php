<?php
/**
 * WordPress Content Import Script
 * 
 * Imports content from Drupal export files into WordPress
 */

require_once 'config.php';

class WordPressImporter {
    private $pdo;
    private $imported_count = 0;
    private $category_cache = [];
    private $tag_cache = [];
    
    public function __construct() {
        try {
            $dsn = "mysql:host=" . WP_DB_HOST . ";dbname=" . WP_DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, WP_DB_USER, WP_DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            migration_log("Connected to WordPress database successfully");
        } catch (PDOException $e) {
            migration_log("Database connection failed: " . $e->getMessage(), LOG_ERROR);
            exit(1);
        }
    }
    
    /**
     * Import all content to WordPress
     */
    public function importAll() {
        migration_log("Starting WordPress content import...");
        
        // Import taxonomy first
        $this->importTaxonomy();
        
        // Import users
        $this->importUsers();
        
        // Import nodes (posts/pages)
        $this->importNodes();
        
        // Import comments
        $this->importComments();
        
        migration_log("Import completed. Total items imported: {$this->imported_count}");
    }
    
    /**
     * Import nodes as WordPress posts/pages
     */
    private function importNodes() {
        global $content_type_mapping, $field_mapping, $user_mapping;
        
        $filename = EXPORT_DIR . 'nodes.json';
        if (!file_exists($filename)) {
            migration_log("Nodes export file not found: {$filename}", LOG_ERROR);
            return;
        }
        
        $nodes = json_decode(file_get_contents($filename), true);
        migration_log("Importing " . count($nodes) . " nodes...");
        
        foreach ($nodes as $node) {
            try {
                $mapping = $content_type_mapping[$node['type']];
                
                // Prepare post data
                $post_data = [
                    'post_title' => $this->sanitizeText($node['title']),
                    'post_content' => $this->processContent($node['body_value']),
                    'post_excerpt' => $this->sanitizeText($node['body_summary']),
                    'post_status' => $node['status'] ? $mapping['wp_status'] : 'draft',
                    'post_type' => $mapping['wp_post_type'],
                    'post_author' => isset($user_mapping[$node['uid']]) ? $user_mapping[$node['uid']] : DEFAULT_WP_AUTHOR,
                    'post_date' => date('Y-m-d H:i:s', $node['created']),
                    'post_modified' => date('Y-m-d H:i:s', $node['changed']),
                    'comment_status' => $node['comment'] == 2 ? 'open' : 'closed',
                    'ping_status' => 'closed',
                    'post_name' => $this->generateSlug($node['title'], $node['url_alias'])
                ];
                
                // Insert post
                $post_id = $this->insertPost($post_data);
                
                if ($post_id) {
                    // Add category if specified
                    if ($mapping['wp_category']) {
                        $this->assignCategory($post_id, $mapping['wp_category']);
                    }
                    
                    // Add taxonomy terms as tags
                    $this->assignTaxonomyAsTerms($post_id, $node['taxonomy']);
                    
                    // Add custom fields
                    $this->addCustomFields($post_id, $node);
                    
                    // Store Drupal ID for reference
                    $this->addPostMeta($post_id, '_drupal_nid', $node['nid']);
                    $this->addPostMeta($post_id, '_drupal_type', $node['type']);
                    
                    $this->imported_count++;
                    migration_log("Imported node {$node['nid']} as post {$post_id}: {$node['title']}");
                } else {
                    migration_log("Failed to import node {$node['nid']}: {$node['title']}", LOG_ERROR);
                }
                
            } catch (Exception $e) {
                migration_log("Error importing node {$node['nid']}: " . $e->getMessage(), LOG_ERROR);
            }
        }
    }
    
    /**
     * Insert WordPress post
     */
    private function insertPost($post_data) {
        $sql = "
            INSERT INTO " . WP_DB_PREFIX . "posts (
                post_author, post_date, post_date_gmt, post_content, post_title, 
                post_excerpt, post_status, comment_status, ping_status, post_name,
                post_modified, post_modified_gmt, post_type
            ) VALUES (
                :post_author, :post_date, :post_date, :post_content, :post_title,
                :post_excerpt, :post_status, :comment_status, :ping_status, :post_name,
                :post_modified, :post_modified, :post_type
            )
        ";
        
        $stmt = $this->pdo->prepare($sql);
        
        if ($stmt->execute($post_data)) {
            return $this->pdo->lastInsertId();
        }
        
        return false;
    }
    
    /**
     * Process content - clean HTML and handle media
     */
    private function processContent($content) {
        if (empty($content)) return '';
        
        // Convert Drupal-specific markup
        $content = $this->convertDrupalMarkup($content);
        
        // Update file paths
        $content = $this->updateFilePaths($content);
        
        // Clean up HTML
        $content = $this->cleanHtml($content);
        
        return $content;
    }
    
    /**
     * Convert Drupal-specific markup to WordPress
     */
    private function convertDrupalMarkup($content) {
        // Convert Drupal media tokens [[{"fid":"30",...}]]
        $content = preg_replace_callback(
            '/\[\[\{[^}]*"fid":"(\d+)"[^}]*\}\]\]/',
            [$this, 'convertMediaToken'],
            $content
        );
        
        // Convert other Drupal-specific elements as needed
        
        return $content;
    }
    
    /**
     * Convert Drupal media token to WordPress shortcode
     */
    private function convertMediaToken($matches) {
        $fid = $matches[1];
        // This would need to be implemented based on your media migration strategy
        return "[gallery id=\"{$fid}\"]"; // Placeholder
    }
    
    /**
     * Update file paths from Drupal to WordPress
     */
    private function updateFilePaths($content) {
        // Update image and file paths
        $content = str_replace(
            'sites/default/files/',
            'wp-content/uploads/',
            $content
        );
        
        return $content;
    }
    
    /**
     * Clean HTML content
     */
    private function cleanHtml($content) {
        // Remove or convert problematic HTML
        $content = strip_tags($content, '<p><br><strong><em><ul><ol><li><a><img><h1><h2><h3><h4><h5><h6><blockquote><iframe>');
        
        // Fix common issues
        $content = str_replace(['<br>', '<br />'], '<br />', $content);
        
        return trim($content);
    }
    
    /**
     * Generate WordPress slug
     */
    private function generateSlug($title, $alias = null) {
        if ($alias) {
            return sanitize_title($alias);
        }
        
        return sanitize_title($title);
    }
    
    /**
     * Sanitize text content
     */
    private function sanitizeText($text) {
        return htmlspecialchars_decode(strip_tags($text));
    }
    
    /**
     * Assign category to post
     */
    private function assignCategory($post_id, $category_name) {
        $category_id = $this->getOrCreateCategory($category_name);
        
        if ($category_id) {
            $sql = "
                INSERT INTO " . WP_DB_PREFIX . "term_relationships (object_id, term_taxonomy_id) 
                VALUES (?, ?)
                ON DUPLICATE KEY UPDATE term_order = term_order
            ";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$post_id, $category_id]);
        }
    }
    
    /**
     * Get or create WordPress category
     */
    private function getOrCreateCategory($name) {
        if (isset($this->category_cache[$name])) {
            return $this->category_cache[$name];
        }
        
        // Check if category exists
        $sql = "
            SELECT tt.term_taxonomy_id 
            FROM " . WP_DB_PREFIX . "terms t
            JOIN " . WP_DB_PREFIX . "term_taxonomy tt ON t.term_id = tt.term_id
            WHERE t.name = ? AND tt.taxonomy = 'category'
        ";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$name]);
        $result = $stmt->fetch();
        
        if ($result) {
            $this->category_cache[$name] = $result['term_taxonomy_id'];
            return $result['term_taxonomy_id'];
        }
        
        // Create new category
        $slug = sanitize_title($name);
        
        // Insert term
        $sql = "INSERT INTO " . WP_DB_PREFIX . "terms (name, slug) VALUES (?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$name, $slug]);
        $term_id = $this->pdo->lastInsertId();
        
        // Insert term taxonomy
        $sql = "INSERT INTO " . WP_DB_PREFIX . "term_taxonomy (term_id, taxonomy) VALUES (?, 'category')";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$term_id]);
        $term_taxonomy_id = $this->pdo->lastInsertId();
        
        $this->category_cache[$name] = $term_taxonomy_id;
        return $term_taxonomy_id;
    }
    
    /**
     * Add post meta
     */
    private function addPostMeta($post_id, $meta_key, $meta_value) {
        $sql = "
            INSERT INTO " . WP_DB_PREFIX . "postmeta (post_id, meta_key, meta_value)
            VALUES (?, ?, ?)
        ";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$post_id, $meta_key, $meta_value]);
    }

    /**
     * Assign taxonomy terms as WordPress tags
     */
    private function assignTaxonomyAsTerms($post_id, $taxonomy_terms) {
        if (empty($taxonomy_terms)) return;

        foreach ($taxonomy_terms as $term) {
            $tag_id = $this->getOrCreateTag($term['name']);
            if ($tag_id) {
                $sql = "
                    INSERT INTO " . WP_DB_PREFIX . "term_relationships (object_id, term_taxonomy_id)
                    VALUES (?, ?)
                    ON DUPLICATE KEY UPDATE term_order = term_order
                ";
                $stmt = $this->pdo->prepare($sql);
                $stmt->execute([$post_id, $tag_id]);
            }
        }
    }

    /**
     * Get or create WordPress tag
     */
    private function getOrCreateTag($name) {
        if (isset($this->tag_cache[$name])) {
            return $this->tag_cache[$name];
        }

        // Check if tag exists
        $sql = "
            SELECT tt.term_taxonomy_id
            FROM " . WP_DB_PREFIX . "terms t
            JOIN " . WP_DB_PREFIX . "term_taxonomy tt ON t.term_id = tt.term_id
            WHERE t.name = ? AND tt.taxonomy = 'post_tag'
        ";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$name]);
        $result = $stmt->fetch();

        if ($result) {
            $this->tag_cache[$name] = $result['term_taxonomy_id'];
            return $result['term_taxonomy_id'];
        }

        // Create new tag
        $slug = sanitize_title($name);

        // Insert term
        $sql = "INSERT INTO " . WP_DB_PREFIX . "terms (name, slug) VALUES (?, ?)";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$name, $slug]);
        $term_id = $this->pdo->lastInsertId();

        // Insert term taxonomy
        $sql = "INSERT INTO " . WP_DB_PREFIX . "term_taxonomy (term_id, taxonomy) VALUES (?, 'post_tag')";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$term_id]);
        $term_taxonomy_id = $this->pdo->lastInsertId();

        $this->tag_cache[$name] = $term_taxonomy_id;
        return $term_taxonomy_id;
    }

    /**
     * Add custom fields from Drupal
     */
    private function addCustomFields($post_id, $node) {
        if (empty($node['custom_fields'])) return;

        foreach ($node['custom_fields'] as $field_name => $field_data) {
            foreach ($field_data as $field_item) {
                // Extract field value based on field type
                $value = $this->extractFieldValue($field_item, $field_name);
                if ($value !== null) {
                    $this->addPostMeta($post_id, "drupal_{$field_name}", $value);
                }
            }
        }
    }

    /**
     * Extract field value based on field type
     */
    private function extractFieldValue($field_item, $field_name) {
        // Handle different field types
        foreach ($field_item as $key => $value) {
            if (strpos($key, '_value') !== false && $value !== null) {
                return $value;
            }
        }
        return null;
    }

    /**
     * Import taxonomy terms
     */
    private function importTaxonomy() {
        $filename = EXPORT_DIR . 'taxonomy.json';
        if (!file_exists($filename)) {
            migration_log("Taxonomy export file not found: {$filename}", LOG_WARNING);
            return;
        }

        $terms = json_decode(file_get_contents($filename), true);
        migration_log("Processing " . count($terms) . " taxonomy terms...");

        // Group terms by vocabulary for better organization
        $vocabularies = [];
        foreach ($terms as $term) {
            $vocab = $term['vocabulary_machine_name'];
            if (!isset($vocabularies[$vocab])) {
                $vocabularies[$vocab] = [];
            }
            $vocabularies[$vocab][] = $term;
        }

        // Create WordPress categories/tags from vocabularies
        foreach ($vocabularies as $vocab_name => $vocab_terms) {
            migration_log("Processing vocabulary: {$vocab_name}");

            foreach ($vocab_terms as $term) {
                // Create as tag by default, can be customized
                $this->getOrCreateTag($term['name']);
            }
        }
    }

    /**
     * Import users
     */
    private function importUsers() {
        $filename = EXPORT_DIR . 'users.json';
        if (!file_exists($filename)) {
            migration_log("Users export file not found: {$filename}", LOG_WARNING);
            return;
        }

        $users = json_decode(file_get_contents($filename), true);
        migration_log("Processing " . count($users) . " users...");

        foreach ($users as $user) {
            // Check if user already exists
            $sql = "SELECT ID FROM " . WP_DB_PREFIX . "users WHERE user_email = ?";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$user['mail']]);

            if (!$stmt->fetch()) {
                // Create new user
                $user_data = [
                    'user_login' => $user['name'],
                    'user_email' => $user['mail'],
                    'user_registered' => date('Y-m-d H:i:s', $user['created']),
                    'user_status' => $user['status'],
                    'display_name' => $user['name']
                ];

                $sql = "
                    INSERT INTO " . WP_DB_PREFIX . "users (
                        user_login, user_email, user_registered, user_status, display_name
                    ) VALUES (
                        :user_login, :user_email, :user_registered, :user_status, :display_name
                    )
                ";

                $stmt = $this->pdo->prepare($sql);
                if ($stmt->execute($user_data)) {
                    $wp_user_id = $this->pdo->lastInsertId();

                    // Add user meta for Drupal reference
                    $this->addUserMeta($wp_user_id, '_drupal_uid', $user['uid']);

                    migration_log("Imported user: {$user['name']} (Drupal UID: {$user['uid']} -> WP ID: {$wp_user_id})");
                }
            }
        }
    }

    /**
     * Add user meta
     */
    private function addUserMeta($user_id, $meta_key, $meta_value) {
        $sql = "
            INSERT INTO " . WP_DB_PREFIX . "usermeta (user_id, meta_key, meta_value)
            VALUES (?, ?, ?)
        ";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$user_id, $meta_key, $meta_value]);
    }

    /**
     * Import comments
     */
    private function importComments() {
        $filename = EXPORT_DIR . 'comments.json';
        if (!file_exists($filename)) {
            migration_log("Comments export file not found: {$filename}", LOG_WARNING);
            return;
        }

        $comments = json_decode(file_get_contents($filename), true);
        migration_log("Processing " . count($comments) . " comments...");

        foreach ($comments as $comment) {
            // Find corresponding WordPress post
            $sql = "
                SELECT post_id FROM " . WP_DB_PREFIX . "postmeta
                WHERE meta_key = '_drupal_nid' AND meta_value = ?
            ";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$comment['nid']]);
            $post_result = $stmt->fetch();

            if ($post_result) {
                $comment_data = [
                    'comment_post_ID' => $post_result['post_id'],
                    'comment_author' => $comment['name'] ?: 'Anonymous',
                    'comment_author_email' => $comment['mail'] ?: '',
                    'comment_author_url' => $comment['homepage'] ?: '',
                    'comment_author_IP' => $comment['hostname'] ?: '',
                    'comment_date' => date('Y-m-d H:i:s', $comment['created']),
                    'comment_content' => $this->processContent($comment['comment_body_value']),
                    'comment_approved' => $comment['status'],
                    'comment_parent' => $comment['pid'] ?: 0
                ];

                $sql = "
                    INSERT INTO " . WP_DB_PREFIX . "comments (
                        comment_post_ID, comment_author, comment_author_email,
                        comment_author_url, comment_author_IP, comment_date,
                        comment_content, comment_approved, comment_parent
                    ) VALUES (
                        :comment_post_ID, :comment_author, :comment_author_email,
                        :comment_author_url, :comment_author_IP, :comment_date,
                        :comment_content, :comment_approved, :comment_parent
                    )
                ";

                $stmt = $this->pdo->prepare($sql);
                if ($stmt->execute($comment_data)) {
                    migration_log("Imported comment {$comment['cid']} for post {$post_result['post_id']}");
                }
            }
        }
    }
}

/**
 * WordPress-compatible sanitize_title function
 */
function sanitize_title($title) {
    $title = strtolower($title);
    $title = preg_replace('/[^a-z0-9\-_]/', '-', $title);
    $title = preg_replace('/-+/', '-', $title);
    $title = trim($title, '-');
    return $title;
}

// Run the import
if (php_sapi_name() === 'cli') {
    $importer = new WordPressImporter();
    $importer->importAll();
} else {
    echo "This script must be run from command line\n";
}
