<?php
/**
 * Main Migration Runner Script
 * 
 * Orchestrates the complete migration process from Drupal to WordPress
 */

require_once 'config.php';

class MigrationRunner {
    private $start_time;
    
    public function __construct() {
        $this->start_time = microtime(true);
        migration_log("=== DRUPAL TO WORDPRESS MIGRATION STARTED ===");
        migration_log("Start time: " . date('Y-m-d H:i:s'));
    }
    
    /**
     * Run complete migration process
     */
    public function runFullMigration() {
        try {
            // Step 1: Export from Drupal
            migration_log("\n--- STEP 1: EXPORTING FROM DRUPAL ---");
            $this->runDrupalExport();
            
            // Step 2: Import to WordPress
            migration_log("\n--- STEP 2: IMPORTING TO WORDPRESS ---");
            $this->runWordPressImport();
            
            // Step 3: Migrate Media
            migration_log("\n--- STEP 3: MIGRATING MEDIA FILES ---");
            $this->runMediaMigration();
            
            // Step 4: Cleanup and Optimization
            migration_log("\n--- STEP 4: CLEANUP AND OPTIMIZATION ---");
            $this->runCleanup();
            
            // Step 5: Generate Report
            migration_log("\n--- STEP 5: GENERATING FINAL REPORT ---");
            $this->generateFinalReport();
            
            $this->logCompletionStats();
            
        } catch (Exception $e) {
            migration_log("MIGRATION FAILED: " . $e->getMessage(), LOG_ERROR);
            exit(1);
        }
    }
    
    /**
     * Run Drupal export
     */
    private function runDrupalExport() {
        migration_log("Starting Drupal content export...");
        
        // Include and run the export script
        require_once 'drupal_export.php';
        
        if (class_exists('DrupalExporter')) {
            $exporter = new DrupalExporter();
            $exporter->exportAll();
            migration_log("Drupal export completed successfully");
        } else {
            throw new Exception("DrupalExporter class not found");
        }
    }
    
    /**
     * Run WordPress import
     */
    private function runWordPressImport() {
        migration_log("Starting WordPress content import...");
        
        // Check if export files exist
        $required_files = ['nodes.json'];
        foreach ($required_files as $file) {
            if (!file_exists(EXPORT_DIR . $file)) {
                throw new Exception("Required export file not found: {$file}");
            }
        }
        
        // Include and run the import script
        require_once 'wordpress_import.php';
        
        if (class_exists('WordPressImporter')) {
            $importer = new WordPressImporter();
            $importer->importAll();
            migration_log("WordPress import completed successfully");
        } else {
            throw new Exception("WordPressImporter class not found");
        }
    }
    
    /**
     * Run media migration
     */
    private function runMediaMigration() {
        if (!MIGRATE_MEDIA) {
            migration_log("Media migration skipped (disabled in config)");
            return;
        }
        
        migration_log("Starting media file migration...");
        
        // Include and run the media migration script
        require_once 'media_migration.php';
        
        if (class_exists('MediaMigrator')) {
            $migrator = new MediaMigrator();
            $migrator->migrateAll();
            $migrator->updateContentReferences();
            migration_log("Media migration completed successfully");
        } else {
            throw new Exception("MediaMigrator class not found");
        }
    }
    
    /**
     * Run cleanup tasks
     */
    private function runCleanup() {
        migration_log("Starting cleanup and optimization...");
        
        // Include and run the cleanup script
        require_once 'cleanup.php';
        
        if (class_exists('MigrationCleanup')) {
            $cleanup = new MigrationCleanup();
            $cleanup->runCleanup();
            migration_log("Cleanup completed successfully");
        } else {
            throw new Exception("MigrationCleanup class not found");
        }
    }
    
    /**
     * Generate final migration report
     */
    private function generateFinalReport() {
        migration_log("Generating final migration report...");
        
        require_once 'cleanup.php';
        
        if (class_exists('MigrationCleanup')) {
            $cleanup = new MigrationCleanup();
            $report = $cleanup->generateReport();
            
            // Display summary
            migration_log("=== MIGRATION SUMMARY ===");
            migration_log("Published Posts: " . $report['published_posts']);
            migration_log("Pages: " . $report['pages']);
            migration_log("Comments: " . $report['approved_comments']);
            migration_log("Users: " . $report['users']);
            migration_log("Media Files: " . $report['media_files']);
            
            migration_log("Content Types Migrated:");
            foreach ($report['content_types'] as $type) {
                migration_log("  - {$type['drupal_type']}: {$type['count']}");
            }
        }
    }
    
    /**
     * Log completion statistics
     */
    private function logCompletionStats() {
        $end_time = microtime(true);
        $duration = $end_time - $this->start_time;
        $duration_formatted = gmdate("H:i:s", $duration);
        
        migration_log("\n=== MIGRATION COMPLETED SUCCESSFULLY ===");
        migration_log("End time: " . date('Y-m-d H:i:s'));
        migration_log("Total duration: {$duration_formatted}");
        migration_log("Peak memory usage: " . $this->formatBytes(memory_get_peak_usage(true)));
        
        // Check log file size
        if (file_exists(MIGRATION_LOG_FILE)) {
            $log_size = filesize(MIGRATION_LOG_FILE);
            migration_log("Log file size: " . $this->formatBytes($log_size));
        }
        
        migration_log("\nNext steps:");
        migration_log("1. Review the migration_report.md file");
        migration_log("2. Test your WordPress site thoroughly");
        migration_log("3. Set up URL redirects from old Drupal URLs");
        migration_log("4. Configure your WordPress theme and plugins");
        migration_log("5. Update DNS when ready to go live");
    }
    
    /**
     * Format bytes to human readable format
     */
    private function formatBytes($bytes, $precision = 2) {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
    
    /**
     * Run specific migration step
     */
    public function runStep($step) {
        switch ($step) {
            case 'export':
                $this->runDrupalExport();
                break;
            case 'import':
                $this->runWordPressImport();
                break;
            case 'media':
                $this->runMediaMigration();
                break;
            case 'cleanup':
                $this->runCleanup();
                break;
            case 'report':
                $this->generateFinalReport();
                break;
            default:
                migration_log("Unknown step: {$step}", LOG_ERROR);
                migration_log("Available steps: export, import, media, cleanup, report");
                exit(1);
        }
    }
}

// Command line interface
if (php_sapi_name() === 'cli') {
    $runner = new MigrationRunner();
    
    // Check command line arguments
    if (isset($argv[1])) {
        $step = $argv[1];
        migration_log("Running migration step: {$step}");
        $runner->runStep($step);
    } else {
        // Run full migration
        migration_log("Running full migration process...");
        $runner->runFullMigration();
    }
} else {
    echo "This script must be run from command line\n";
    echo "Usage: php run_migration.php [step]\n";
    echo "Steps: export, import, media, cleanup, report\n";
    echo "Or run without arguments for full migration\n";
}
