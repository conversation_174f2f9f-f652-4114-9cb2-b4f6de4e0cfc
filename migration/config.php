<?php
/**
 * Migration Configuration
 * 
 * Database connections and migration settings for Drupal to WordPress migration
 */

// Drupal Database Configuration
define('DRUPAL_DB_HOST', 'localhost');
define('DRUPAL_DB_NAME', 'aralmsmt_prox');
define('DRUPAL_DB_USER', 'your_drupal_user');
define('DRUPAL_DB_PASS', 'your_drupal_password');
define('DRUPAL_DB_PREFIX', ''); // Usually empty for Drupal

// WordPress Database Configuration  
define('WP_DB_HOST', 'localhost');
define('WP_DB_NAME', 'aralmsmt_wp557');
define('WP_DB_USER', 'aralmsmt_migration_user');
define('WP_DB_PASS', 'J#yIBZ8I4FAnq');
define('WP_DB_PREFIX', 'wpax_'); // Usually wp_

// Migration Settings
define('MIGRATION_BATCH_SIZE', 50); // Number of posts to process at once
define('MIGRATION_LOG_FILE', 'migration.log');
define('EXPORT_DIR', 'exports/');
define('MEDIA_DIR', 'media/');

// Content Type Mappings
$content_type_mapping = [
    'article' => [
        'wp_post_type' => 'post',
        'wp_category' => 'Articles',
        'wp_status' => 'publish'
    ],
    'page' => [
        'wp_post_type' => 'page', 
        'wp_category' => null,
        'wp_status' => 'publish'
    ],
    'events' => [
        'wp_post_type' => 'post',
        'wp_category' => 'Events',
        'wp_status' => 'publish'
    ],
    'latest_news' => [
        'wp_post_type' => 'post',
        'wp_category' => 'News', 
        'wp_status' => 'publish'
    ],
    'success_stories' => [
        'wp_post_type' => 'post',
        'wp_category' => 'Success Stories',
        'wp_status' => 'publish'
    ],
    'gallery' => [
        'wp_post_type' => 'post',
        'wp_category' => 'Gallery',
        'wp_status' => 'publish'
    ],
    'slideshow' => [
        'wp_post_type' => 'post',
        'wp_category' => 'Slideshow',
        'wp_status' => 'draft' // Review before publishing
    ],
    'webform' => [
        'wp_post_type' => 'page',
        'wp_category' => null,
        'wp_status' => 'publish'
    ]
];

// Field Mappings
$field_mapping = [
    'title' => 'post_title',
    'body_value' => 'post_content', 
    'body_summary' => 'post_excerpt',
    'created' => 'post_date',
    'changed' => 'post_modified',
    'uid' => 'post_author',
    'status' => 'post_status'
];

// User ID mapping (Drupal UID to WordPress user ID)
$user_mapping = [
    1 => 1, // Admin user
    // Add more mappings as needed
];

// Default WordPress author if Drupal user not found
define('DEFAULT_WP_AUTHOR', 1);

// Media settings
define('DRUPAL_FILES_PATH', '/sites/default/files/');
define('WP_UPLOADS_PATH', '/wp-content/uploads/');
define('MIGRATE_MEDIA', true);

// Logging levels
define('LOG_ERROR', 1);
define('LOG_WARNING', 2); 
define('LOG_INFO', 3);
define('LOG_DEBUG', 4);
define('LOG_LEVEL', LOG_INFO);

/**
 * Logging function
 */
function migration_log($message, $level = LOG_INFO) {
    if ($level <= LOG_LEVEL) {
        $timestamp = date('Y-m-d H:i:s');
        $level_names = [
            LOG_ERROR => 'ERROR',
            LOG_WARNING => 'WARNING', 
            LOG_INFO => 'INFO',
            LOG_DEBUG => 'DEBUG'
        ];
        $log_entry = "[$timestamp] [{$level_names[$level]}] $message\n";
        file_put_contents(MIGRATION_LOG_FILE, $log_entry, FILE_APPEND);
        echo $log_entry;
    }
}

/**
 * Create export directory if it doesn't exist
 */
if (!is_dir(EXPORT_DIR)) {
    mkdir(EXPORT_DIR, 0755, true);
}

if (!is_dir(MEDIA_DIR)) {
    mkdir(MEDIA_DIR, 0755, true);
}
