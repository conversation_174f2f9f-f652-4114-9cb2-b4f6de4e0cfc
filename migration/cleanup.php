<?php
/**
 * Post-Migration Cleanup Script
 * 
 * Performs cleanup tasks after migration is complete
 */

require_once 'config.php';

class MigrationCleanup {
    private $wp_pdo;
    
    public function __construct() {
        try {
            $dsn = "mysql:host=" . WP_DB_HOST . ";dbname=" . WP_DB_NAME . ";charset=utf8mb4";
            $this->wp_pdo = new PDO($dsn, WP_DB_USER, WP_DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            migration_log("Connected to WordPress database for cleanup");
        } catch (PDOException $e) {
            migration_log("Database connection failed: " . $e->getMessage(), LOG_ERROR);
            exit(1);
        }
    }
    
    /**
     * Run all cleanup tasks
     */
    public function runCleanup() {
        migration_log("Starting post-migration cleanup...");
        
        $this->updateTermCounts();
        $this->updateCommentCounts();
        $this->cleanupOrphanedMeta();
        $this->generateSitemaps();
        $this->updatePermalinks();
        $this->optimizeDatabase();
        
        migration_log("Cleanup completed successfully");
    }
    
    /**
     * Update term counts for categories and tags
     */
    private function updateTermCounts() {
        migration_log("Updating term counts...");
        
        // Update category counts
        $sql = "
            UPDATE " . WP_DB_PREFIX . "term_taxonomy tt
            SET count = (
                SELECT COUNT(*) 
                FROM " . WP_DB_PREFIX . "term_relationships tr
                JOIN " . WP_DB_PREFIX . "posts p ON tr.object_id = p.ID
                WHERE tr.term_taxonomy_id = tt.term_taxonomy_id
                AND p.post_status = 'publish'
                AND p.post_type IN ('post', 'page')
            )
            WHERE tt.taxonomy IN ('category', 'post_tag')
        ";
        
        $this->wp_pdo->exec($sql);
        migration_log("Term counts updated");
    }
    
    /**
     * Update comment counts for posts
     */
    private function updateCommentCounts() {
        migration_log("Updating comment counts...");
        
        $sql = "
            UPDATE " . WP_DB_PREFIX . "posts p
            SET comment_count = (
                SELECT COUNT(*) 
                FROM " . WP_DB_PREFIX . "comments c
                WHERE c.comment_post_ID = p.ID
                AND c.comment_approved = '1'
            )
            WHERE p.post_type IN ('post', 'page')
        ";
        
        $this->wp_pdo->exec($sql);
        migration_log("Comment counts updated");
    }
    
    /**
     * Clean up orphaned metadata
     */
    private function cleanupOrphanedMeta() {
        migration_log("Cleaning up orphaned metadata...");
        
        // Remove orphaned post meta
        $sql = "
            DELETE pm FROM " . WP_DB_PREFIX . "postmeta pm
            LEFT JOIN " . WP_DB_PREFIX . "posts p ON pm.post_id = p.ID
            WHERE p.ID IS NULL
        ";
        $stmt = $this->wp_pdo->prepare($sql);
        $stmt->execute();
        $deleted_postmeta = $stmt->rowCount();
        
        // Remove orphaned user meta
        $sql = "
            DELETE um FROM " . WP_DB_PREFIX . "usermeta um
            LEFT JOIN " . WP_DB_PREFIX . "users u ON um.user_id = u.ID
            WHERE u.ID IS NULL
        ";
        $stmt = $this->wp_pdo->prepare($sql);
        $stmt->execute();
        $deleted_usermeta = $stmt->rowCount();
        
        // Remove orphaned term relationships
        $sql = "
            DELETE tr FROM " . WP_DB_PREFIX . "term_relationships tr
            LEFT JOIN " . WP_DB_PREFIX . "posts p ON tr.object_id = p.ID
            WHERE p.ID IS NULL
        ";
        $stmt = $this->wp_pdo->prepare($sql);
        $stmt->execute();
        $deleted_term_rels = $stmt->rowCount();
        
        migration_log("Cleaned up orphaned metadata: {$deleted_postmeta} postmeta, {$deleted_usermeta} usermeta, {$deleted_term_rels} term relationships");
    }
    
    /**
     * Generate XML sitemaps (basic implementation)
     */
    private function generateSitemaps() {
        migration_log("Generating XML sitemaps...");
        
        // Get all published posts and pages
        $sql = "
            SELECT post_name, post_modified, post_type
            FROM " . WP_DB_PREFIX . "posts
            WHERE post_status = 'publish'
            AND post_type IN ('post', 'page')
            ORDER BY post_modified DESC
        ";
        
        $stmt = $this->wp_pdo->query($sql);
        $posts = $stmt->fetchAll();
        
        // Generate sitemap XML
        $sitemap = '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
        $sitemap .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">' . "\n";
        
        foreach ($posts as $post) {
            $url = 'https://yoursite.com/' . ($post['post_type'] === 'page' ? '' : 'blog/') . $post['post_name'] . '/';
            $lastmod = date('Y-m-d', strtotime($post['post_modified']));
            
            $sitemap .= "  <url>\n";
            $sitemap .= "    <loc>{$url}</loc>\n";
            $sitemap .= "    <lastmod>{$lastmod}</lastmod>\n";
            $sitemap .= "    <changefreq>weekly</changefreq>\n";
            $sitemap .= "    <priority>0.8</priority>\n";
            $sitemap .= "  </url>\n";
        }
        
        $sitemap .= '</urlset>';
        
        // Save sitemap
        file_put_contents('sitemap.xml', $sitemap);
        migration_log("XML sitemap generated with " . count($posts) . " URLs");
    }
    
    /**
     * Update permalink structure
     */
    private function updatePermalinks() {
        migration_log("Updating permalink structure...");
        
        // Set WordPress options for permalinks
        $options = [
            'permalink_structure' => '/%postname%/',
            'category_base' => 'category',
            'tag_base' => 'tag'
        ];
        
        foreach ($options as $option_name => $option_value) {
            $sql = "
                INSERT INTO " . WP_DB_PREFIX . "options (option_name, option_value, autoload)
                VALUES (?, ?, 'yes')
                ON DUPLICATE KEY UPDATE option_value = VALUES(option_value)
            ";
            $stmt = $this->wp_pdo->prepare($sql);
            $stmt->execute([$option_name, $option_value]);
        }
        
        migration_log("Permalink structure updated");
    }
    
    /**
     * Optimize database tables
     */
    private function optimizeDatabase() {
        migration_log("Optimizing database tables...");
        
        $tables = [
            WP_DB_PREFIX . 'posts',
            WP_DB_PREFIX . 'postmeta',
            WP_DB_PREFIX . 'comments',
            WP_DB_PREFIX . 'terms',
            WP_DB_PREFIX . 'term_taxonomy',
            WP_DB_PREFIX . 'term_relationships',
            WP_DB_PREFIX . 'users',
            WP_DB_PREFIX . 'usermeta'
        ];
        
        foreach ($tables as $table) {
            try {
                $this->wp_pdo->exec("OPTIMIZE TABLE {$table}");
                migration_log("Optimized table: {$table}");
            } catch (PDOException $e) {
                migration_log("Failed to optimize table {$table}: " . $e->getMessage(), LOG_WARNING);
            }
        }
    }
    
    /**
     * Generate migration report
     */
    public function generateReport() {
        migration_log("Generating migration report...");
        
        $report = [];
        
        // Count migrated content
        $sql = "SELECT COUNT(*) as count FROM " . WP_DB_PREFIX . "posts WHERE post_status = 'publish'";
        $stmt = $this->wp_pdo->query($sql);
        $report['published_posts'] = $stmt->fetch()['count'];
        
        $sql = "SELECT COUNT(*) as count FROM " . WP_DB_PREFIX . "posts WHERE post_type = 'page'";
        $stmt = $this->wp_pdo->query($sql);
        $report['pages'] = $stmt->fetch()['count'];
        
        $sql = "SELECT COUNT(*) as count FROM " . WP_DB_PREFIX . "comments WHERE comment_approved = '1'";
        $stmt = $this->wp_pdo->query($sql);
        $report['approved_comments'] = $stmt->fetch()['count'];
        
        $sql = "SELECT COUNT(*) as count FROM " . WP_DB_PREFIX . "users";
        $stmt = $this->wp_pdo->query($sql);
        $report['users'] = $stmt->fetch()['count'];
        
        $sql = "SELECT COUNT(*) as count FROM " . WP_DB_PREFIX . "posts WHERE post_type = 'attachment'";
        $stmt = $this->wp_pdo->query($sql);
        $report['media_files'] = $stmt->fetch()['count'];
        
        // Count by content type
        $sql = "
            SELECT pm.meta_value as drupal_type, COUNT(*) as count
            FROM " . WP_DB_PREFIX . "postmeta pm
            JOIN " . WP_DB_PREFIX . "posts p ON pm.post_id = p.ID
            WHERE pm.meta_key = '_drupal_type'
            GROUP BY pm.meta_value
        ";
        $stmt = $this->wp_pdo->query($sql);
        $report['content_types'] = $stmt->fetchAll();
        
        // Generate report file
        $report_content = "# Migration Report\n\n";
        $report_content .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        $report_content .= "## Summary\n\n";
        $report_content .= "- Published Posts: {$report['published_posts']}\n";
        $report_content .= "- Pages: {$report['pages']}\n";
        $report_content .= "- Approved Comments: {$report['approved_comments']}\n";
        $report_content .= "- Users: {$report['users']}\n";
        $report_content .= "- Media Files: {$report['media_files']}\n\n";
        
        $report_content .= "## Content Types Migrated\n\n";
        foreach ($report['content_types'] as $type) {
            $report_content .= "- {$type['drupal_type']}: {$type['count']}\n";
        }
        
        $report_content .= "\n## Next Steps\n\n";
        $report_content .= "1. Review migrated content for formatting issues\n";
        $report_content .= "2. Test all internal links\n";
        $report_content .= "3. Verify media files are displaying correctly\n";
        $report_content .= "4. Set up URL redirects from old Drupal URLs\n";
        $report_content .= "5. Configure WordPress theme and plugins\n";
        $report_content .= "6. Test contact forms and other functionality\n";
        $report_content .= "7. Update DNS when ready to go live\n";
        
        file_put_contents('migration_report.md', $report_content);
        migration_log("Migration report generated: migration_report.md");
        
        return $report;
    }
}

// Run cleanup
if (php_sapi_name() === 'cli') {
    $cleanup = new MigrationCleanup();
    $cleanup->runCleanup();
    $cleanup->generateReport();
} else {
    echo "This script must be run from command line\n";
}
