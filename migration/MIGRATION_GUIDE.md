# Drupal to WordPress Migration Guide

This guide will walk you through migrating your Drupal 7 site to WordPress using the provided migration scripts.

## Prerequisites

- PHP 7.4 or higher
- MySQL/MariaDB access to both Drupal and WordPress databases
- WordPress installation ready for import
- Command line access
- Sufficient disk space for exports and media files

## Pre-Migration Checklist

### 1. Backup Everything
```bash
# Backup Drupal database
mysqldump -u username -p drupal_database > drupal_backup.sql

# Backup Drupal files
tar -czf drupal_files_backup.tar.gz /path/to/drupal/sites/default/files/

# Backup WordPress database (if existing)
mysqldump -u username -p wordpress_database > wordpress_backup.sql
```

### 2. Prepare WordPress
- Install fresh WordPress or ensure existing installation is ready
- Install any required plugins (optional)
- Note database credentials

### 3. Configure Migration Settings
Edit `config.php` with your database credentials and settings:

```php
// Drupal Database
define('DRUPAL_DB_HOST', 'localhost');
define('DRUPAL_DB_NAME', 'aralmsmt_prox');
define('DRUPAL_DB_USER', 'your_drupal_user');
define('DRUPAL_DB_PASS', 'your_drupal_password');

// WordPress Database  
define('WP_DB_HOST', 'localhost');
define('WP_DB_NAME', 'your_wordpress_db');
define('WP_DB_USER', 'your_wp_user');
define('WP_DB_PASS', 'your_wp_password');
```

## Migration Process

### Option 1: Full Automated Migration

Run the complete migration process:

```bash
cd migration/
php run_migration.php
```

This will:
1. Export all content from Drupal
2. Import content to WordPress
3. Migrate media files
4. Run cleanup and optimization
5. Generate a final report

### Option 2: Step-by-Step Migration

Run individual steps for more control:

```bash
# Step 1: Export from Drupal
php run_migration.php export

# Step 2: Import to WordPress
php run_migration.php import

# Step 3: Migrate media files
php run_migration.php media

# Step 4: Cleanup and optimize
php run_migration.php cleanup

# Step 5: Generate report
php run_migration.php report
```

## Content Mapping

The migration will map Drupal content types to WordPress as follows:

| Drupal Content Type | WordPress Post Type | WordPress Category |
|-------------------|-------------------|------------------|
| article | post | Articles |
| page | page | - |
| events | post | Events |
| latest_news | post | News |
| success_stories | post | Success Stories |
| gallery | post | Gallery |
| slideshow | post | Slideshow |
| webform | page | - |

## What Gets Migrated

### Content
- ✅ All published nodes (articles, pages, events, etc.)
- ✅ Node titles and body content
- ✅ Publication dates and modification dates
- ✅ Author information
- ✅ Custom fields (as post meta)
- ✅ URL aliases (as post slugs)

### Taxonomy
- ✅ Taxonomy terms (converted to WordPress tags)
- ✅ Term relationships

### Users
- ✅ User accounts
- ✅ User metadata
- ✅ User roles (basic mapping)

### Comments
- ✅ Published comments
- ✅ Comment metadata
- ✅ Comment threading

### Media
- ✅ File uploads
- ✅ Image files with thumbnails
- ✅ File metadata
- ✅ Updated content references

## Post-Migration Tasks

### 1. Review Content
- Check that all content migrated correctly
- Verify formatting and HTML structure
- Test embedded media and images
- Review custom field data

### 2. Configure WordPress
```bash
# Set permalink structure
# Go to WordPress Admin > Settings > Permalinks
# Choose "Post name" structure
```

### 3. Set Up URL Redirects
Create `.htaccess` rules to redirect old Drupal URLs:

```apache
# Redirect Drupal node URLs to WordPress
RedirectMatch 301 ^/node/([0-9]+)/?$ /migrated-post-slug/

# Redirect Drupal taxonomy URLs
RedirectMatch 301 ^/taxonomy/term/([0-9]+)/?$ /category/category-name/
```

### 4. Test Functionality
- Test all internal links
- Verify contact forms work
- Check image galleries
- Test search functionality
- Verify RSS feeds

### 5. Theme and Design
- Install and configure WordPress theme
- Customize design to match original site
- Set up menus and widgets
- Configure homepage and blog page

## Troubleshooting

### Common Issues

**Database Connection Errors**
```bash
# Check database credentials in config.php
# Verify database server is running
# Test connection manually
```

**Memory Limit Errors**
```bash
# Increase PHP memory limit
php -d memory_limit=512M run_migration.php
```

**File Permission Errors**
```bash
# Ensure proper permissions
chmod 755 migration/
chmod 644 migration/*.php
```

**Large Dataset Issues**
```bash
# Adjust batch size in config.php
define('MIGRATION_BATCH_SIZE', 25); // Reduce from 50
```

### Log Files

Check `migration.log` for detailed information about the migration process:

```bash
tail -f migration.log
```

### Rollback

If you need to start over:

```bash
# Restore WordPress database backup
mysql -u username -p wordpress_database < wordpress_backup.sql

# Clear migration files
rm -rf exports/
rm -rf media/
rm migration.log
```

## Performance Optimization

### Before Migration
- Ensure adequate server resources
- Close unnecessary applications
- Use local database connections when possible

### During Migration
- Monitor system resources
- Check log files for errors
- Be patient with large datasets

### After Migration
- Optimize WordPress database
- Install caching plugins
- Optimize images
- Set up CDN if needed

## Security Considerations

- Use strong database passwords
- Limit database user permissions
- Remove migration scripts from production server
- Update WordPress and plugins after migration
- Change default WordPress admin credentials

## Support

If you encounter issues:

1. Check the migration log file
2. Review the generated migration report
3. Verify database connections
4. Check file permissions
5. Ensure sufficient disk space and memory

## Final Checklist

- [ ] All content migrated successfully
- [ ] Media files displaying correctly
- [ ] URL redirects configured
- [ ] WordPress theme configured
- [ ] Menus and navigation set up
- [ ] Contact forms tested
- [ ] Search functionality working
- [ ] SSL certificate configured
- [ ] Backup strategy in place
- [ ] DNS updated (when ready to go live)

## Estimated Timeline

- Small site (< 100 posts): 1-2 hours
- Medium site (100-1000 posts): 2-4 hours  
- Large site (1000+ posts): 4-8 hours

*Times include migration, testing, and basic WordPress configuration.*
