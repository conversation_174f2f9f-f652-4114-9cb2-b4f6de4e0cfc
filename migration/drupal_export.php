<?php
/**
 * Drupal Content Export Script
 * 
 * Exports content from Drupal 7 database for WordPress migration
 */

require_once 'config.php';

class DrupalExporter {
    private $pdo;
    private $exported_count = 0;
    
    public function __construct() {
        try {
            $dsn = "mysql:host=" . DRUPAL_DB_HOST . ";dbname=" . DRUPAL_DB_NAME . ";charset=utf8mb4";
            $this->pdo = new PDO($dsn, DRUPAL_DB_USER, DRUPAL_DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            migration_log("Connected to Drupal database successfully");
        } catch (PDOException $e) {
            migration_log("Database connection failed: " . $e->getMessage(), LOG_ERROR);
            exit(1);
        }
    }
    
    /**
     * Export all content from Drupal
     */
    public function exportAll() {
        migration_log("Starting Drupal content export...");
        
        // Export nodes (articles, pages, etc.)
        $this->exportNodes();
        
        // Export taxonomy terms
        $this->exportTaxonomy();
        
        // Export users
        $this->exportUsers();
        
        // Export comments
        $this->exportComments();
        
        // Export media/files
        $this->exportMedia();
        
        migration_log("Export completed. Total items exported: {$this->exported_count}");
    }
    
    /**
     * Export nodes (content)
     */
    private function exportNodes() {
        global $content_type_mapping;
        
        migration_log("Exporting nodes...");
        
        // Get all published nodes with their content
        $sql = "
            SELECT 
                n.nid,
                n.vid, 
                n.type,
                n.language,
                n.title,
                n.uid,
                n.status,
                n.created,
                n.changed,
                n.comment,
                n.promote,
                n.sticky,
                nr.log as revision_log,
                fdb.body_value,
                fdb.body_summary,
                fdb.body_format
            FROM node n
            LEFT JOIN node_revision nr ON n.vid = nr.vid
            LEFT JOIN field_data_body fdb ON n.nid = fdb.entity_id 
                AND fdb.entity_type = 'node' 
                AND fdb.deleted = 0
            WHERE n.status = 1
            ORDER BY n.created ASC
        ";
        
        $stmt = $this->pdo->query($sql);
        $nodes = $stmt->fetchAll();
        
        $exported_nodes = [];
        
        foreach ($nodes as $node) {
            // Skip if content type not in mapping
            if (!isset($content_type_mapping[$node['type']])) {
                migration_log("Skipping node {$node['nid']} - unmapped content type: {$node['type']}", LOG_WARNING);
                continue;
            }
            
            // Get additional fields for this node
            $node['custom_fields'] = $this->getNodeCustomFields($node['nid']);
            
            // Get taxonomy terms
            $node['taxonomy'] = $this->getNodeTaxonomy($node['nid']);
            
            // Get URL alias
            $node['url_alias'] = $this->getNodeAlias($node['nid']);
            
            $exported_nodes[] = $node;
            $this->exported_count++;
        }
        
        // Save to JSON file
        $filename = EXPORT_DIR . 'nodes.json';
        file_put_contents($filename, json_encode($exported_nodes, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        migration_log("Exported " . count($exported_nodes) . " nodes to {$filename}");
    }
    
    /**
     * Get custom fields for a node
     */
    private function getNodeCustomFields($nid) {
        $fields = [];
        
        // Get all field data tables
        $sql = "SHOW TABLES LIKE 'field_data_%'";
        $stmt = $this->pdo->query($sql);
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        foreach ($tables as $table) {
            if ($table === 'field_data_body') continue; // Already handled
            
            $field_sql = "
                SELECT * FROM {$table} 
                WHERE entity_type = 'node' 
                AND entity_id = ? 
                AND deleted = 0
            ";
            $field_stmt = $this->pdo->prepare($field_sql);
            $field_stmt->execute([$nid]);
            $field_data = $field_stmt->fetchAll();
            
            if ($field_data) {
                $field_name = str_replace('field_data_', '', $table);
                $fields[$field_name] = $field_data;
            }
        }
        
        return $fields;
    }
    
    /**
     * Get taxonomy terms for a node
     */
    private function getNodeTaxonomy($nid) {
        $sql = "
            SELECT 
                td.name,
                td.description,
                tv.name as vocabulary_name,
                tv.machine_name as vocabulary_machine_name
            FROM taxonomy_index ti
            JOIN taxonomy_term_data td ON ti.tid = td.tid
            JOIN taxonomy_vocabulary tv ON td.vid = tv.vid
            WHERE ti.nid = ?
        ";
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute([$nid]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get URL alias for a node
     */
    private function getNodeAlias($nid) {
        $sql = "SELECT alias FROM url_alias WHERE source = ? ORDER BY pid DESC LIMIT 1";
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute(["node/{$nid}"]);
        $result = $stmt->fetch();
        return $result ? $result['alias'] : null;
    }
    
    /**
     * Export taxonomy terms
     */
    private function exportTaxonomy() {
        migration_log("Exporting taxonomy...");
        
        $sql = "
            SELECT 
                td.*,
                tv.name as vocabulary_name,
                tv.machine_name as vocabulary_machine_name,
                tv.description as vocabulary_description
            FROM taxonomy_term_data td
            JOIN taxonomy_vocabulary tv ON td.vid = tv.vid
            ORDER BY tv.machine_name, td.weight, td.name
        ";
        
        $stmt = $this->pdo->query($sql);
        $terms = $stmt->fetchAll();
        
        $filename = EXPORT_DIR . 'taxonomy.json';
        file_put_contents($filename, json_encode($terms, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        migration_log("Exported " . count($terms) . " taxonomy terms to {$filename}");
    }
    
    /**
     * Export users
     */
    private function exportUsers() {
        migration_log("Exporting users...");
        
        $sql = "
            SELECT 
                uid,
                name,
                mail,
                created,
                access,
                login,
                status,
                timezone,
                language,
                init
            FROM users 
            WHERE uid > 0
            ORDER BY created ASC
        ";
        
        $stmt = $this->pdo->query($sql);
        $users = $stmt->fetchAll();
        
        $filename = EXPORT_DIR . 'users.json';
        file_put_contents($filename, json_encode($users, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        migration_log("Exported " . count($users) . " users to {$filename}");
    }
    
    /**
     * Export comments
     */
    private function exportComments() {
        migration_log("Exporting comments...");
        
        $sql = "
            SELECT 
                c.*,
                fdc.comment_body_value,
                fdc.comment_body_format
            FROM comment c
            LEFT JOIN field_data_comment_body fdc ON c.cid = fdc.entity_id 
                AND fdc.entity_type = 'comment'
            WHERE c.status = 1
            ORDER BY c.created ASC
        ";
        
        $stmt = $this->pdo->query($sql);
        $comments = $stmt->fetchAll();
        
        $filename = EXPORT_DIR . 'comments.json';
        file_put_contents($filename, json_encode($comments, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        migration_log("Exported " . count($comments) . " comments to {$filename}");
    }
    
    /**
     * Export media and files
     */
    private function exportMedia() {
        migration_log("Exporting media files...");
        
        $sql = "
            SELECT 
                fm.*,
                fu.module,
                fu.type as usage_type,
                fu.id as usage_id,
                fu.count as usage_count
            FROM file_managed fm
            LEFT JOIN file_usage fu ON fm.fid = fu.fid
            WHERE fm.status = 1
            ORDER BY fm.timestamp ASC
        ";
        
        $stmt = $this->pdo->query($sql);
        $files = $stmt->fetchAll();
        
        $filename = EXPORT_DIR . 'media.json';
        file_put_contents($filename, json_encode($files, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
        migration_log("Exported " . count($files) . " media files to {$filename}");
    }
}

// Run the export
if (php_sapi_name() === 'cli') {
    $exporter = new DrupalExporter();
    $exporter->exportAll();
} else {
    echo "This script must be run from command line\n";
}
