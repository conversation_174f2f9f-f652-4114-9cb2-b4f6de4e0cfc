<?php
/**
 * Media Migration Script
 * 
 * Handles migration of files and images from Drupal to WordPress
 */

require_once 'config.php';

class MediaMigrator {
    private $drupal_files_path;
    private $wp_uploads_path;
    private $wp_pdo;
    private $migrated_count = 0;
    
    public function __construct() {
        // Set up file paths
        $this->drupal_files_path = rtrim(DRUPAL_FILES_PATH, '/');
        $this->wp_uploads_path = rtrim(WP_UPLOADS_PATH, '/');
        
        // WordPress database connection
        try {
            $dsn = "mysql:host=" . WP_DB_HOST . ";dbname=" . WP_DB_NAME . ";charset=utf8mb4";
            $this->wp_pdo = new PDO($dsn, WP_DB_USER, WP_DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
            ]);
            migration_log("Connected to WordPress database for media migration");
        } catch (PDOException $e) {
            migration_log("Database connection failed: " . $e->getMessage(), LOG_ERROR);
            exit(1);
        }
    }
    
    /**
     * Migrate all media files
     */
    public function migrateAll() {
        if (!MIGRATE_MEDIA) {
            migration_log("Media migration disabled in config");
            return;
        }
        
        migration_log("Starting media migration...");
        
        $filename = EXPORT_DIR . 'media.json';
        if (!file_exists($filename)) {
            migration_log("Media export file not found: {$filename}", LOG_ERROR);
            return;
        }
        
        $media_files = json_decode(file_get_contents($filename), true);
        migration_log("Processing " . count($media_files) . " media files...");
        
        foreach ($media_files as $file) {
            $this->migrateFile($file);
        }
        
        migration_log("Media migration completed. Files migrated: {$this->migrated_count}");
    }
    
    /**
     * Migrate individual file
     */
    private function migrateFile($file) {
        try {
            $drupal_path = $file['uri'];
            $filename = basename($drupal_path);
            
            // Remove 'public://' or 'private://' prefix
            $relative_path = str_replace(['public://', 'private://'], '', $drupal_path);
            
            // Source file path
            $source_path = $this->drupal_files_path . '/' . $relative_path;
            
            // Destination path in WordPress uploads
            $upload_date = date('Y/m', $file['timestamp']);
            $dest_dir = $this->wp_uploads_path . '/' . $upload_date;
            $dest_path = $dest_dir . '/' . $filename;
            
            // Create destination directory if it doesn't exist
            if (!is_dir($dest_dir)) {
                mkdir($dest_dir, 0755, true);
            }
            
            // Copy file if source exists
            if (file_exists($source_path)) {
                if (copy($source_path, $dest_path)) {
                    // Create WordPress attachment
                    $attachment_id = $this->createWordPressAttachment($file, $upload_date . '/' . $filename);
                    
                    if ($attachment_id) {
                        migration_log("Migrated file: {$filename} (Drupal FID: {$file['fid']} -> WP ID: {$attachment_id})");
                        $this->migrated_count++;
                    }
                } else {
                    migration_log("Failed to copy file: {$source_path}", LOG_ERROR);
                }
            } else {
                migration_log("Source file not found: {$source_path}", LOG_WARNING);
            }
            
        } catch (Exception $e) {
            migration_log("Error migrating file {$file['fid']}: " . $e->getMessage(), LOG_ERROR);
        }
    }
    
    /**
     * Create WordPress attachment entry
     */
    private function createWordPressAttachment($file, $wp_file_path) {
        $filename = basename($file['filename']);
        $mime_type = $file['filemime'];
        $file_size = $file['filesize'];
        
        // Prepare attachment data
        $attachment_data = [
            'post_title' => pathinfo($filename, PATHINFO_FILENAME),
            'post_content' => '',
            'post_excerpt' => '',
            'post_status' => 'inherit',
            'post_type' => 'attachment',
            'post_mime_type' => $mime_type,
            'post_date' => date('Y-m-d H:i:s', $file['timestamp']),
            'post_modified' => date('Y-m-d H:i:s', $file['timestamp'])
        ];
        
        // Insert attachment post
        $sql = "
            INSERT INTO " . WP_DB_PREFIX . "posts (
                post_title, post_content, post_excerpt, post_status, 
                post_type, post_mime_type, post_date, post_modified
            ) VALUES (
                :post_title, :post_content, :post_excerpt, :post_status,
                :post_type, :post_mime_type, :post_date, :post_modified
            )
        ";
        
        $stmt = $this->wp_pdo->prepare($sql);
        
        if ($stmt->execute($attachment_data)) {
            $attachment_id = $this->wp_pdo->lastInsertId();
            
            // Add attachment metadata
            $this->addAttachmentMeta($attachment_id, '_wp_attached_file', $wp_file_path);
            $this->addAttachmentMeta($attachment_id, '_drupal_fid', $file['fid']);
            $this->addAttachmentMeta($attachment_id, '_drupal_uri', $file['uri']);
            
            // Add image metadata if it's an image
            if (strpos($mime_type, 'image/') === 0) {
                $this->addImageMetadata($attachment_id, $this->wp_uploads_path . '/' . $wp_file_path);
            }
            
            return $attachment_id;
        }
        
        return false;
    }
    
    /**
     * Add attachment metadata
     */
    private function addAttachmentMeta($attachment_id, $meta_key, $meta_value) {
        $sql = "
            INSERT INTO " . WP_DB_PREFIX . "postmeta (post_id, meta_key, meta_value) 
            VALUES (?, ?, ?)
        ";
        $stmt = $this->wp_pdo->prepare($sql);
        $stmt->execute([$attachment_id, $meta_key, $meta_value]);
    }
    
    /**
     * Add image metadata for WordPress
     */
    private function addImageMetadata($attachment_id, $file_path) {
        if (!file_exists($file_path)) {
            return;
        }
        
        $image_info = getimagesize($file_path);
        if ($image_info) {
            $metadata = [
                'width' => $image_info[0],
                'height' => $image_info[1],
                'file' => basename($file_path),
                'sizes' => []
            ];
            
            // Generate thumbnail sizes (WordPress standard sizes)
            $this->generateThumbnails($file_path, $metadata);
            
            $this->addAttachmentMeta($attachment_id, '_wp_attachment_metadata', serialize($metadata));
        }
    }
    
    /**
     * Generate WordPress thumbnail sizes
     */
    private function generateThumbnails($file_path, &$metadata) {
        $thumbnail_sizes = [
            'thumbnail' => [150, 150, true],
            'medium' => [300, 300, false],
            'medium_large' => [768, 0, false],
            'large' => [1024, 1024, false]
        ];
        
        foreach ($thumbnail_sizes as $size_name => $size_config) {
            list($max_width, $max_height, $crop) = $size_config;
            
            $thumb_path = $this->createThumbnail($file_path, $max_width, $max_height, $crop, $size_name);
            
            if ($thumb_path) {
                $thumb_info = getimagesize($thumb_path);
                $metadata['sizes'][$size_name] = [
                    'file' => basename($thumb_path),
                    'width' => $thumb_info[0],
                    'height' => $thumb_info[1],
                    'mime-type' => $thumb_info['mime']
                ];
            }
        }
    }
    
    /**
     * Create thumbnail image
     */
    private function createThumbnail($source_path, $max_width, $max_height, $crop, $size_name) {
        $image_info = getimagesize($source_path);
        if (!$image_info) {
            return false;
        }
        
        list($orig_width, $orig_height, $image_type) = $image_info;
        
        // Calculate new dimensions
        if ($crop) {
            $new_width = $max_width;
            $new_height = $max_height;
        } else {
            $ratio = min($max_width / $orig_width, $max_height / $orig_height);
            $new_width = intval($orig_width * $ratio);
            $new_height = intval($orig_height * $ratio);
        }
        
        // Skip if image is smaller than thumbnail
        if ($orig_width <= $new_width && $orig_height <= $new_height) {
            return false;
        }
        
        // Create image resource
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $source_image = imagecreatefromjpeg($source_path);
                break;
            case IMAGETYPE_PNG:
                $source_image = imagecreatefrompng($source_path);
                break;
            case IMAGETYPE_GIF:
                $source_image = imagecreatefromgif($source_path);
                break;
            default:
                return false;
        }
        
        if (!$source_image) {
            return false;
        }
        
        // Create thumbnail
        $thumb_image = imagecreatetruecolor($new_width, $new_height);
        
        // Preserve transparency for PNG and GIF
        if ($image_type == IMAGETYPE_PNG || $image_type == IMAGETYPE_GIF) {
            imagealphablending($thumb_image, false);
            imagesavealpha($thumb_image, true);
            $transparent = imagecolorallocatealpha($thumb_image, 255, 255, 255, 127);
            imagefilledrectangle($thumb_image, 0, 0, $new_width, $new_height, $transparent);
        }
        
        imagecopyresampled($thumb_image, $source_image, 0, 0, 0, 0, $new_width, $new_height, $orig_width, $orig_height);
        
        // Generate thumbnail filename
        $path_info = pathinfo($source_path);
        $thumb_filename = $path_info['filename'] . '-' . $new_width . 'x' . $new_height . '.' . $path_info['extension'];
        $thumb_path = $path_info['dirname'] . '/' . $thumb_filename;
        
        // Save thumbnail
        $success = false;
        switch ($image_type) {
            case IMAGETYPE_JPEG:
                $success = imagejpeg($thumb_image, $thumb_path, 90);
                break;
            case IMAGETYPE_PNG:
                $success = imagepng($thumb_image, $thumb_path);
                break;
            case IMAGETYPE_GIF:
                $success = imagegif($thumb_image, $thumb_path);
                break;
        }
        
        imagedestroy($source_image);
        imagedestroy($thumb_image);
        
        return $success ? $thumb_path : false;
    }
    
    /**
     * Update content to reference new WordPress media
     */
    public function updateContentReferences() {
        migration_log("Updating content references to new media...");
        
        // Get all posts with Drupal content
        $sql = "
            SELECT p.ID, p.post_content 
            FROM " . WP_DB_PREFIX . "posts p
            JOIN " . WP_DB_PREFIX . "postmeta pm ON p.ID = pm.post_id
            WHERE pm.meta_key = '_drupal_nid'
            AND p.post_content LIKE '%sites/default/files/%'
        ";
        
        $stmt = $this->wp_pdo->query($sql);
        $posts = $stmt->fetchAll();
        
        foreach ($posts as $post) {
            $updated_content = $this->updateMediaReferences($post['post_content']);
            
            if ($updated_content !== $post['post_content']) {
                $update_sql = "UPDATE " . WP_DB_PREFIX . "posts SET post_content = ? WHERE ID = ?";
                $update_stmt = $this->wp_pdo->prepare($update_sql);
                $update_stmt->execute([$updated_content, $post['ID']]);
                
                migration_log("Updated media references in post {$post['ID']}");
            }
        }
    }
    
    /**
     * Update media references in content
     */
    private function updateMediaReferences($content) {
        // Update file paths
        $content = str_replace(
            'sites/default/files/',
            'wp-content/uploads/',
            $content
        );
        
        // Convert Drupal image styles to WordPress sizes
        $content = preg_replace(
            '/\/styles\/[^\/]+\/public\//',
            '/',
            $content
        );
        
        return $content;
    }
}

// Run the media migration
if (php_sapi_name() === 'cli') {
    $migrator = new MediaMigrator();
    $migrator->migrateAll();
    $migrator->updateContentReferences();
} else {
    echo "This script must be run from command line\n";
}
